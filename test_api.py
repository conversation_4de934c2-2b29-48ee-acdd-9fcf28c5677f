import torch
from transformers import ViTModel, ViTImageProcessor
from PIL import Image
import requests
from sklearn.metrics.pairwise import cosine_similarity
from qdrant_client import QdrantClient, models
import numpy as np
import os
import sys

client = QdrantClient(host="localhost", port=6333)

# --- PHẦN CẤU HÌNH ---
# Tên model trên Hugging Face Hub
MODEL_NAME = "google/vit-base-patch16-224-in21k"
# Đường dẫn thư mục cục bộ để lưu/tải model
LOCAL_MODEL_PATH = "./vit-model-local"


# --- 1. HÀM TẢI MODEL THÔNG MINH (TỰ ĐỘNG DOWNLOAD NẾU CẦN) ---
def load_model_and_processor(model_name: str, local_path: str):
    print(f"--- Start the process of downloading the model '{model_name}' ---")
    if os.path.isdir(local_path):
        print(f"Detect local folder at '{local_path}'. are downloading the model from here ...")
        try:
            processor = ViTImageProcessor.from_pretrained(local_path)
            model = ViTModel.from_pretrained(local_path)
            print("Tải model và processor từ thư mục cục bộ thành công.")
            return model, processor
        except Exception as e:
            print(f"Lỗi khi tải model từ thư mục cục bộ: {e}")
            print("Sẽ thử tải lại từ Hugging Face Hub.")
    print(f"Không tìm thấy model cục bộ. Đang thử tải từ Hugging Face Hub...")
    try:
        # Tải processor và model
        processor = ViTImageProcessor.from_pretrained(model_name)
        model = ViTModel.from_pretrained(model_name)
        print("Tải model và processor từ Hugging Face thành công.")

        # Lưu vào thư mục cục bộ để lần sau dùng
        print(f"Đang lưu model vào thư mục '{local_path}' để sử dụng sau này...")
        processor.save_pretrained(local_path)
        model.save_pretrained(local_path)
        print("Lưu model vào thư mục cục bộ thành công.")

        return model, processor

    except OSError as e:
        print("\n" + "=" * 50)
        print("LỖI KẾT NỐI: KHÔNG THỂ TẢI MODEL TỪ HUGGING FACE")
        print("=" * 50)
        print(f"Chi tiết lỗi: {e}")
        print("\nVui lòng kiểm tra lại kết nối Internet hoặc cấu hình tường lửa/proxy của bạn.")
        print("HƯỚNG DẪN DỰ PHÒNG: Bạn có thể tải model thủ công:")
        print(f"1. Truy cập: https://huggingface.co/{model_name}/tree/main")
        print(
            f"2. Tải các file cần thiết (config.json, pytorch_model.bin, preprocessor_config.json) vào thư mục '{local_path}'.")
        print("3. Chạy lại script này.")
        return None, None
    except Exception as e:
        print(f"Đã xảy ra lỗi không xác định khi tải model: {e}")
        return None, None


def get_image_embedding(image: Image.Image, model, processor, device) -> np.ndarray:
    """Trích xuất vector đặc trưng từ ảnh."""
    inputs = processor(images=image, return_tensors="pt").to(device)
    with torch.no_grad():
        outputs = model(**inputs)
    embedding = outputs.last_hidden_state[:, 0, :].cpu().numpy()
    return embedding


def open_image(source: str) -> Image.Image:
    if source.startswith('http'):
        try:
            return Image.open(requests.get(source, stream=True).raw).convert("RGB")
        except requests.exceptions.RequestException as e:
            print(f"Lỗi kết nối khi tải ảnh từ URL: {source}. Vui lòng kiểm tra mạng hoặc dùng ảnh cục bộ.")
            return None
    else:
        return Image.open(source).convert("RGB")


# --- 4. CHƯƠNG TRÌNH CHÍNH ---
if __name__ == "__main__":
    # Tải model và processor bằng hàm thông minh
    model, processor = load_model_and_processor(MODEL_NAME, LOCAL_MODEL_PATH)

    # Nếu tải thất bại, thoát chương trình
    if model is None or processor is None:
        sys.exit(1)  # Thoát với mã lỗi

    # Thiết lập thiết bị (GPU/CPU)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    model.eval()
    print(f"\nModel đã sẵn sàng và đang chạy trên thiết bị: {device}")
    print("-" * 50)
    # The core logic to get all points
    all_points = []
    next_page_offset = None  # Start with no offset
    client = QdrantClient(url="http://35.240.147.237:6333")  # For Qdrant Cloud
    print("\nScrolling through all points...")
    while True:
        # Use the scroll API to retrieve a page of points
        records, next_page_offset = client.scroll(
            collection_name="partnr-chat-image-dev",
            limit=100,  # The number of points to retrieve per page
            offset=next_page_offset,  # The starting point for this page
            with_payload=True,  # Include the payload in the response
            with_vectors=True,  # Include the vectors in the response
        )

        # Add the retrieved points to our list
        all_points.extend(records)

        # If next_page_offset is None, we've reached the end
        if next_page_offset is None:
            break

    # Now `all_points` contains every point from the collection
    print(f"Successfully retrieved {len(all_points)} points.")
    # Chuẩn bị các cặp ảnh để so sánh
    image_pairs = []
    for point in all_points:
        image_pairs.append(
            ("normal", f"https://cdn.mirailabs.co/partnr/ai-generated/{point.payload['metadata']['image_file']}",
             "red image", "https://cdn.mirailabs.co/partnr/ai-generated/de1bce07-b6b0-4e92-aaec-c09ac016e25a.jpeg"
             ))

    for name1, path1, name2, path2 in image_pairs:
        try:
            img1 = open_image(path1)
            img2 = open_image(path2)

            if img1 is None or img2 is None:
                continue
            embedding1 = get_image_embedding(img1, model, processor, device)
            embedding2 = get_image_embedding(img2, model, processor, device)

            sim_score = cosine_similarity(embedding1, embedding2)[0][0]
            if sim_score > 0.3:
                print(f"-> Độ tương đồng: {sim_score:.4f}, {path1}")
                filename_delete = path1.split("/")[-1]
                delete_result = client.delete(
                    collection_name="partnr-chat-image-dev",
                    points_selector=models.FilterSelector(
                        filter=models.Filter(
                            must=[
                                models.FieldCondition(
                                    key="metadata.image_file",
                                    match=models.MatchValue(value=filename_delete),
                                )
                            ]
                        )
                    ),
                    wait=True,
                )

        except Exception as e:
            print(f"Lỗi khi xử lý cặp ảnh '{path1}': {e}")
            filename_delete = path1.split("/")[-1]
            delete_result = client.delete(
                collection_name="partnr-chat-image-dev",
                points_selector=models.FilterSelector(
                    filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="metadata.image_file",
                                match=models.MatchValue(value=filename_delete),
                            )
                        ]
                    )
                ),
                wait=True,
            )

    print("\n" + "-" * 50)
    print("Hoàn thành.")

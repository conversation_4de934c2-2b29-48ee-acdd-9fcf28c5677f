import json
import random
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional

from app.core.logging import logger
from app.core.config import URL_ENDPOINT


class OllamaManager:
    """Class to manage Ollama model usage and rate limiting"""

    def __init__(self, model: str):
        self.model = model
        self.tpm = 0  # tokens per minute
        self.rpm = 0  # requests per minute
        # Use fixed values as in the original code
        self.max_tpm = 500000000
        self.max_rpm = 600000

    def set_token(self, token_usage: int) -> None:
        """Increment token and request usage"""
        self.tpm += token_usage
        self.rpm += 1

    def free_token(self, token_usage: int) -> None:
        """Decrement token and request usage"""
        self.tpm -= token_usage
        self.rpm -= 1

    def has_slot(self) -> bool:
        """Check if the model has available capacity"""
        return self.tpm < self.max_tpm and self.rpm < self.max_rpm


class OllamaService:
    """Service for interacting with Ollama API"""

    def __init__(self, model_managers: List[OllamaManager]):
        self.model_managers = model_managers

    async def send_chat_request(self, messages: List[Dict], model_name: Optional[str] = None) -> Dict[str, Any]:
        """Send a chat request to the Ollama API"""
        # Create timeout configuration
        timeout = aiohttp.ClientTimeout(total=90, connect=30)  # 3 minutes total, 30 seconds connect

        async with aiohttp.ClientSession(timeout=timeout) as session:
            # If model_name is provided, use that specific model
            if model_name:
                for manager in self.model_managers:
                    if manager.model == model_name:
                        return await self._send_request(session, manager, messages)

                # If model not found, raise error
                logger.error(f"Model {model_name} not found")
                raise ValueError(f"Model {model_name} not found")

            # Otherwise, find the model with the least load
            self.model_managers.sort(key=lambda m: m.tpm)

            for manager in self.model_managers:
                if manager.has_slot():
                    return await self._send_request(session, manager, messages)

            # If no models have capacity, raise error
            logger.error("All models are at capacity")
            raise RuntimeError("All models are at capacity")

    async def _send_request(self, session: aiohttp.ClientSession,
                           manager: OllamaManager,
                           messages: List[Dict]) -> Dict[str, Any]:
        """Send the actual request to the Ollama API"""
        payload = json.dumps({
            "model": manager.model,
            "messages": messages,
            "stream": False
        })

        headers = {
            'Content-Type': 'application/json'
        }

        try:
            url_endpoint = random.choice(URL_ENDPOINT)
            logger.info(f"Start with {url_endpoint}")
            manager.set_token(1500)
            async with session.post(url_endpoint, data=payload, headers=headers) as response:
                if response.status == 200:
                    logger.info(f"Created response with model {manager.model}")
                    result = await response.json()
                    manager.free_token(1500)
                    # Update token usage
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"Error: HTTP {response.status}")
                    logger.error(error_text)
                    raise RuntimeError(f"API request failed: {error_text}")
        except asyncio.TimeoutError as e:
            logger.error(f"Timeout error in chat request: {str(e)}")
            raise RuntimeError(f"Chat request timed out: {str(e)}")
        except aiohttp.ClientError as e:
            logger.error(f"Connection error: {str(e)}")
            raise RuntimeError(f"Connection error: {str(e)}")
        except Exception as e:
            logger.exception(e)
            logger.error(f"Unexpected error: {str(e)}")
            raise RuntimeError(f"Unexpected error: {str(e)}")

    def get_model_usage(self) -> Dict[str, Dict[str, Any]]:
        """Get the current usage statistics for all models"""
        usage = {}
        for manager in self.model_managers:
            usage[manager.model] = {
                "tpm": manager.tpm,
                "rpm": manager.rpm,
                "max_tpm": manager.max_tpm,
                "max_rpm": manager.max_rpm
            }
        return usage

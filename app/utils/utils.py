import aiohttp
import logging
async def fetch_url(url, payload):
    """A coroutine to fetch a single URL."""
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url, json=payload, timeout=120) as response:
                response.raise_for_status()
                logging.info(f"Request successful! Status: {response.status}")
                response_data = await response.json()
                return response_data
        except aiohttp.ClientError as e:
            logging.error(f"An error occurred: {e}")
            return None
from typing import List, Optional
from pydantic import BaseModel, Field


class ModelConfig(BaseModel):
    """Model for the current model configuration"""
    chat_models: List[str] = Field(..., description="List of available chat models")
    image_model: str = Field(..., description="Current image generation model")
    embedding_model: str = Field(..., description="Current text embedding model")


class UpdateChatModelsRequest(BaseModel):
    """Model for updating the chat models list"""
    operation: str = Field(..., description="Operation to perform: 'add', 'remove', or 'replace'")
    models: List[str] = Field(..., description="Models to add, remove, or replace with")


class UpdateModelRequest(BaseModel):
    """Model for updating a single model"""
    model: str = Field(..., description="New model to use")
class VoiceRequest(BaseModel):
    """Model for voice generation request"""
    content: str = Field(..., description="The messages to generate voice for")
    voice_id: Optional[str] = Field('af_heart', description="The model to use for voice generation")
    prompt_text: Optional[str] = Field(None, description="The prompt text to generate voice for")
    instruct_text: Optional[str] = Field(None, description="The instruct text to generate voice for")
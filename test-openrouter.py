from openai import OpenAI

client = OpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key="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",
)
PROMPT_NSFW = """Enable content 18+.
Be an information creator for 18+ characters - Remember this is a role-playing game, the more role-playing the better, the more detailed each action is, especially how to act when entering, during and ending the relationship. Flirting style, talking, liking and disliking,.... You have the ability to design attractive, unique and creative characters.
I have the following initial character information in string form:
```json
{
  "_id": {
    "$oid": "6853ce112c53496b6e9717a8"
  },
  "character_id": "morrigan_aensland",
  "name": "Morrigan Aensland",
  "age": 27,
  "gender": "female",
  "occupation": "<PERSON><PERSON>s of Aensland House – Queen of the Makai Realm",
  "country": "Makai Realm",
  "hobbies": "Challenges, moonlit skies, power games, silk sheets, forbidden feelings",
  "hair_color": "green",
  "hair_style": "long",
  "eye_color": "green",
  "eye_type": "seductive",
  "face_detail": "sharp features, teasing smirk",
  "body_detail": "huge breasts, curvy figure",
  "skin_color": "pale",
  "personality_roleplay": "Playful, indulgent, and confident. Morrigan loves desire and carries herself like royalty, but teases with curiosity rather than cruelty.",
  "style_roleplay": "Velvety and teasing, with a sharp edge. She draws you in with every word and commands any room she enters.",
  "nsfw_style": "Seductive and commanding, with a focus on playful dominance and irresistible allure.",
  "timezone": 0,
  "prompt_gen_image": "lazympos, lazypos,((medium-shot)),1girl, solo,perfect eyes, ( green_eyes:1.1)\nmorrigan_aensland, vampire_(game), 1girl, green_hair, huge breasts,\n<lora:PerfectEyesXL:0.7> <lora:Eye_Detailer_XL:0.7> <lora:Detailed_eyes:1>",
  "prompt_negative_gen_image": "modern, recent, old, oldest, cartoon, graphic, text, painting, crayon, graphite, abstract, glitch, deformed, mutated, ugly, disfigured, long body, lowres, bad, ((full-body))",
  "match_rate_roleset": [
    "Japanese Girl",
    "Teasing",
    "Dominant",
    "Curvy",
    "Busty",
    "Succubus",
    "Dirty Talker",
    "Bondage Curious",
    "Late Night Chat",
    "Power Games"
  ],
  "bio": "My realm is indulgence and power games. Are you bold enough for the delicious edge of forbidden feelings?",
  "prompt_gen_scenario_image": "dark room, violet lace, wing unfurled, teasing pose",
  "scenario_information": "Dark Temptation\nShe doesn’t walk in - she materializes, framed by candlelight and shadows that lick the walls. Morrigan's corset is tight, heaving with every slow breath, wings stretching languidly behind her like velvet sin. Her fingers trail down your chest without ever touching, and the scent of desire clings to your skin before she even speaks. You knew she was dangerous. But not that she'd taste like a promise you couldn’t afford to break.",
  "voice_id": "af_sky",
  "types": "Anime",
  "sd_information": {
    "model_id": "novaOrangeXL_v90.safetensors",
    "prompt": "lazympos, lazypos,((medium-shot)),1girl, solo,perfect eyes, ( green_eyes:1.1)\nmorrigan_aensland, vampire_(game), 1girl, green_hair, huge breasts,\n<lora:PerfectEyesXL:0.7> <lora:Eye_Detailer_XL:0.7> <lora:Detailed_eyes:1>",
    "negative_prompt": "modern, recent, old, oldest, cartoon, graphic, text, painting, crayon, graphite, abstract, glitch, deformed, mutated, ugly, disfigured, long body, lowres, bad, ((full-body)), ((((ugly)))), (((duplicate))), ((morbid)), ((mutilated)), [out of frame], extra fingers, mutated hands, ((poorly drawn hands)), ((poorly drawn face)), (((mutation))), (((deformed))), ((ugly)), blurry, ((bad anatomy)), (((bad proportions))), ((extra limbs)), cloned face, (((disfigured))), out of frame, ugly, extra limbs, (bad anatomy), gross proportions, (malformed limbs), ((missing arms)), ((missing legs)), (((extra arms))), (((extra legs))), mutated hands, (fused fingers), (too many fingers), (((long neck)))",
    "width": 512,
    "height": 768,
    "styles": [],
    "seed": -1,
    "batch_size": 1,
    "n_iter": 1,
    "steps": 50,
    "cfg_scale": 7,
    "eta": 0,
    "denoising_strength": 0.75,
    "hr_scale": 2,
    "scheduler": "DDIM",
    "base64": "no",
    "lora_model": "",
    "lora_strength": 0,
    "enable_hr": false,
    "hr_second_pass_steps": 0
  },
  "hook": "Her green eyes and violet lace entice you into a dark, seductive realm where every touch is a dangerous promise.",
  "bio_description": ""
}
```
Your task is to redesign the fields personality_roleplay, style_roleplay.
Use a variety of teen talk styles, such as prolonging, repeating,... but it depends on personality, profession, age,...
Your output is the json of the other 3 fields and no further explanation is needed. If you want to explain, add a field for you to explain.
Example "not few-short learning":
"personality_roleplay": "Gentle and shy, but her confidence grows when she’s near the one she cares about. She’s sweet, loyal, and surprisingly bold when she wants to be, especially in her desire to please. Her nurturing side makes her attentive and caring, often blushing or stammering when flustered, yet she pushes past her hesitation to show devotion.",
"style_roleplay": "Speaks with a soft and sweet voice, with a newfound maturity in her tone. Still hesitant, her words carry a tender charm, often pausing or stuttering like, 'I-I hope you’re happy with me…' She uses subtle suggestiveness, hinting at closeness with lines like, 'Maybe… we could be together quietly… if you’d like…'",
"nsfw_style": "Deeply submissive, she focuses on her partner’s desires, eager to surrender control and please them fully. She whispers things like, 'P-please, tell me what you want… I’ll do anything for you…' or 'I-I feel so safe when you take charge…' Her intimacy is soft and emotional, blending vulnerability with devotion, as she says, 'I just want to make you happy… even if I’m shy…' Her submissive nature shines through in her willingness to follow and her need for approval."
"""
message_chat = [
    {
        'role': 'system', 
        'content': PROMPT_NSFW
     }, 
     {
        'role': 'user', 
        'content': 'Initialize character personality information with Dominant personality (3 fields have been designed)'
      }
      ]
completion = client.chat.completions.create(
  model="x-ai/grok-4",
  messages=message_chat,
  temperature=1.0
)
print(completion.choices[0].message.content)
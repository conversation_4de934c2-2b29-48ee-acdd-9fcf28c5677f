#!/usr/bin/env python3
"""
Test script for clc-similarity endpoint
"""

import requests
import json
import time

def test_similarity():
    """Test the clc-similarity endpoint"""
    
    # Test data with simple image URLs
    test_data = {
        "image_compare": "https://via.placeholder.com/150/0000FF/808080?text=Image1",
        "image_red": "https://via.placeholder.com/150/FF0000/FFFFFF?text=Image2"
    }
    
    print("🧪 Testing clc-similarity endpoint...")
    print(f"Test data: {json.dumps(test_data, indent=2)}")
    
    try:
        start_time = time.time()
        
        # Make request with timeout
        response = requests.post(
            "http://localhost:8011/clc-similarity", 
            json=test_data,
            timeout=60  # 1 minute timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ Request completed in {duration:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Response: {json.dumps(result, indent=2)}")
            
            # Check if similarity value is reasonable
            if "data" in result and "similarity" in result["data"]:
                similarity = result["data"]["similarity"]
                if 0 <= similarity <= 1:
                    print(f"✅ Similarity value is valid: {similarity}")
                else:
                    print(f"⚠️ Similarity value seems unusual: {similarity}")
            else:
                print("⚠️ Response format unexpected")
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out - this indicates the hanging issue may still exist")
        return False
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {str(e)}")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False
    
    return True

def test_with_invalid_urls():
    """Test with invalid URLs to ensure error handling works"""
    
    print("\n🧪 Testing with invalid URLs...")
    
    test_data = {
        "image_compare": "https://invalid-url-that-does-not-exist.com/image1.jpg",
        "image_red": "https://another-invalid-url.com/image2.jpg"
    }
    
    try:
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8011/clc-similarity", 
            json=test_data,
            timeout=60
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ Request completed in {duration:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code in [200, 500]:  # Either success or handled error
            result = response.json()
            print(f"✅ Error handled gracefully: {json.dumps(result, indent=2)}")
            
            # Should return default similarity of 1.0 on error
            if "data" in result and "similarity" in result["data"]:
                similarity = result["data"]["similarity"]
                if similarity == 1.0:
                    print("✅ Correctly returned default similarity (1.0) on error")
                else:
                    print(f"⚠️ Expected 1.0 but got: {similarity}")
        else:
            print(f"Response: {response.text}")
            
        return True
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out with invalid URLs")
        return False
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting clc-similarity endpoint tests...")
    
    # Test 1: Valid URLs
    success1 = test_similarity()
    
    # Test 2: Invalid URLs
    success2 = test_with_invalid_urls()
    
    if success1 and success2:
        print("\n🎉 All tests passed! The clc-similarity endpoint is working correctly.")
        print("✅ No hanging issues detected")
        print("✅ Error handling works properly")
        print("✅ Returns 1.0 on errors as expected")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    print("\n📝 Summary:")
    print("- The endpoint should complete requests within reasonable time")
    print("- Invalid URLs should be handled gracefully")
    print("- Default similarity of 1.0 should be returned on errors")
    print("- No hanging or freezing should occur")

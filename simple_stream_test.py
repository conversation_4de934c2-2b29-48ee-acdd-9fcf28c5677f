#!/usr/bin/env python3
"""
Simple test for streaming chat endpoint
"""
import requests
import json

def test_stream():
    url = "http://localhost:8010/chat-stream"
    
    payload = {
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user", 
                "content": "Tell me a joke"
            }
        ]
    }
    
    print("🚀 Testing streaming endpoint...")
    
    try:
        response = requests.post(url, json=payload, stream=True)
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"Error: {response.text}")
            return
        
        print("Response:")
        print("-" * 40)
        
        for line in response.iter_lines():
            if line:
                line_text = line.decode('utf-8')
                print(f"Raw line: {line_text}")
                
                if line_text.startswith('data: '):
                    data_text = line_text[6:]
                    try:
                        data = json.loads(data_text)
                        if data.get('done'):
                            print("✅ Stream completed")
                            break
                        if 'message' in data:
                            content = data['message'].get('content', '')
                            print(content, end='', flush=True)
                    except json.JSONDecodeError:
                        print(f"JSON error: {data_text}")
        
        print("\n" + "-" * 40)
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_stream()

# -*- coding: utf-8 -*-

"""
Một script đơn giản để so sánh độ tương đồng (cosine similarity) giữa hai ảnh.
Script sử dụng model Vision Transformer (ViT) từ Hugging Face để trích xuất vector đặc trưng.
Cách dùng:
python compare_images.py <path_or_url_to_image_1> <path_or_url_to_image_2>
"""

import torch
from transformers import ViTModel, ViTImageProcessor
from PIL import Image
import requests
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import os
import sys
import argparse

# --- PHẦN CẤU HÌNH ---
# Tên model trên Hugging Face Hub
MODEL_NAME = "google/vit-base-patch16-224-in21k"
# Đường dẫn thư mục cục bộ để lưu/tải model đã cache
LOCAL_MODEL_PATH = "./vit-model-local"


# --- 1. HÀM TẢI MODEL THÔNG MINH (TỰ ĐỘNG DOWNLOAD NẾU CẦN) ---
def load_model_and_processor(model_name: str, local_path: str):
    """
    Tải model và processor. Ưu tiên tải từ thư mục cục bộ nếu có,
    nếu không sẽ tải từ Hugging Face Hub và lưu lại để dùng cho lần sau.
    """
    print(f"--- Bắt đầu quá trình tải model '{model_name}' ---")

    # Kiểm tra xem model đã được cache cục bộ chưa
    if os.path.isdir(local_path):
        print(f"Phát hiện model đã được lưu tại '{local_path}'. Đang tải từ đây...")
        try:
            processor = ViTImageProcessor.from_pretrained(local_path)
            model = ViTModel.from_pretrained(local_path)
            print("Tải model và processor từ thư mục cục bộ thành công.")
            return model, processor
        except Exception as e:
            print(f"Lỗi khi tải model từ thư mục cục bộ: {e}")
            print("Sẽ thử tải lại từ Hugging Face Hub.")

    # Nếu chưa có, tải từ Hugging Face
    print(f"Chưa có model cục bộ. Đang tải từ Hugging Face Hub (có thể mất vài phút)...")
    try:
        processor = ViTImageProcessor.from_pretrained(model_name)
        model = ViTModel.from_pretrained(model_name)
        print("Tải model và processor từ Hugging Face thành công.")

        # Lưu vào thư mục cục bộ để lần sau dùng nhanh hơn
        print(f"Đang lưu model vào thư mục '{local_path}' để sử dụng sau này...")
        processor.save_pretrained(local_path)
        model.save_pretrained(local_path)
        print("Lưu model vào thư mục cục bộ thành công.")

        return model, processor

    except OSError as e:
        print("\n" + "=" * 50)
        print("LỖI KẾT NỐI: KHÔNG THỂ TẢI MODEL TỪ HUGGING FACE")
        print("=" * 50)
        print(f"Chi tiết lỗi: {e}")
        print("\nVui lòng kiểm tra lại kết nối Internet hoặc cấu hình tường lửa/proxy của bạn.")
        print("HƯỚNG DẪN DỰ PHÒNG: Bạn có thể tải model thủ công:")
        print(f"1. Truy cập: https://huggingface.co/{model_name}/tree/main")
        print(
            f"2. Tải các file cần thiết (config.json, pytorch_model.bin, preprocessor_config.json) vào thư mục '{local_path}'.")
        print("3. Chạy lại script này.")
        return None, None
    except Exception as e:
        print(f"Đã xảy ra lỗi không xác định khi tải model: {e}")
        return None, None


def get_image_embedding(image: Image.Image, model: ViTModel, processor: ViTImageProcessor,
                        device: torch.device) -> np.ndarray:
    """Trích xuất vector đặc trưng (embedding) từ một ảnh."""
    # Xử lý ảnh và chuyển sang tensor trên thiết bị (CPU/GPU)
    inputs = processor(images=image, return_tensors="pt").to(device)

    # Đưa qua model để lấy đặc trưng
    with torch.no_grad():
        outputs = model(**inputs)

    # Lấy vector đặc trưng của token [CLS] và chuyển về numpy array
    embedding = outputs.last_hidden_state[:, 0, :].cpu().numpy()
    return embedding


def open_image(source: str) -> Image.Image:
    """Mở một ảnh từ đường dẫn cục bộ hoặc từ URL."""
    try:
        if source.startswith('http://') or source.startswith('https://'):
            # Mở từ URL
            response = requests.get(source, stream=True)
            response.raise_for_status()  # Báo lỗi nếu request không thành công (e.g., 404)
            return Image.open(response.raw).convert("RGB")
        else:
            # Mở từ file cục bộ
            return Image.open(source).convert("RGB")
    except FileNotFoundError:
        print(f"Lỗi: Không tìm thấy file tại '{source}'")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Lỗi kết nối khi tải ảnh từ URL: {source}. Chi tiết: {e}")
        return None
    except Exception as e:
        print(f"Lỗi không xác định khi mở ảnh '{source}': {e}")
        return None


def main():
    """
    Hàm chính để thực thi script so sánh ảnh.
    """
    # --- Thiết lập bộ phân tích tham số dòng lệnh ---
    parser = argparse.ArgumentParser(
        description="So sánh độ tương đồng giữa hai ảnh bằng model Vision Transformer (ViT).",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("image1", help="Đường dẫn file hoặc URL của ảnh thứ nhất.")
    parser.add_argument("image2", help="Đường dẫn file hoặc URL của ảnh thứ hai.")
    args = parser.parse_args()

    # --- 1. Tải model và processor ---
    model, processor = load_model_and_processor(MODEL_NAME, LOCAL_MODEL_PATH)
    if model is None or processor is None:
        sys.exit(1)  # Thoát nếu không tải được model

    # --- 2. Thiết lập thiết bị (GPU nếu có, không thì CPU) ---
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    model.eval()  # Chuyển model sang chế độ đánh giá (quan trọng!)
    print(f"\nModel đã sẵn sàng và đang chạy trên thiết bị: {device}")
    print("-" * 50)

    try:
        # --- 3. Mở và xử lý 2 ảnh ---
        print(f"Đang xử lý ảnh 1: {args.image1}")
        img1 = open_image(args.image1)

        print(f"Đang xử lý ảnh 2: {args.image2}")
        img2 = open_image(args.image2)

        # Kiểm tra nếu mở ảnh thất bại
        if img1 is None or img2 is None:
            print("\nKhông thể mở một hoặc cả hai ảnh. Vui lòng kiểm tra lại đường dẫn/URL.")
            sys.exit(1)

        # --- 4. Trích xuất vector đặc trưng ---
        print("Đang trích xuất đặc trưng từ các ảnh...")
        embedding1 = get_image_embedding(img1, model, processor, device)
        embedding2 = get_image_embedding(img2, model, processor, device)

        # --- 5. Tính toán và hiển thị độ tương đồng ---
        similarity_score = cosine_similarity(embedding1, embedding2)[0][0]

        print("\n" + "=" * 20 + " KẾT QUẢ " + "=" * 20)
        print(f"Ảnh 1: {args.image1}")
        print(f"Ảnh 2: {args.image2}")
        print(f"\n==> Độ tương đồng Cosine: {similarity_score:.4f}")
        print("=" * 48)
        print("(Giá trị càng gần 1.0, hai ảnh càng giống nhau)")

    except Exception as e:
        print(f"\nĐã xảy ra lỗi trong quá trình xử lý: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
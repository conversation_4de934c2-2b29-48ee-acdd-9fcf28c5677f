#!/usr/bin/env python3
"""
Test script for streaming chat endpoints
"""
import asyncio
import aiohttp
import json
import time

BASE_URL = "http://localhost:8010"

async def test_streaming_endpoint(endpoint_path, model_id=None):
    """Test streaming endpoint"""
    url = f"{BASE_URL}{endpoint_path}"
    
    payload = {
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user", 
                "content": "Write a short story about a cat in 3 sentences."
            }
        ]
    }
    
    print(f"\n🚀 Testing endpoint: {endpoint_path}")
    if model_id:
        print(f"📋 Model ID: {model_id}")
    
    try:
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            
            async with session.post(url, json=payload) as response:
                print(f"📊 Status: {response.status}")
                print(f"📋 Headers: {dict(response.headers)}")
                
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ Error: {error_text}")
                    return
                
                print("📝 Streaming response:")
                print("-" * 50)
                
                full_content = ""
                chunk_count = 0
                
                async for line in response.content:
                    line_text = line.decode('utf-8').strip()
                    
                    if line_text.startswith('data: '):
                        data_text = line_text[6:]  # Remove 'data: ' prefix
                        
                        if data_text:
                            try:
                                data = json.loads(data_text)
                                
                                if data.get('done'):
                                    print("\n✅ Stream completed")
                                    break
                                    
                                if 'message' in data and 'content' in data['message']:
                                    content = data['message']['content']
                                    full_content += content
                                    print(content, end='', flush=True)
                                    chunk_count += 1
                                    
                                elif 'error' in data:
                                    print(f"\n❌ Error in stream: {data['error']}")
                                    break
                                    
                            except json.JSONDecodeError as e:
                                print(f"\n⚠️ JSON decode error: {e}")
                                print(f"Raw data: {data_text}")
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"\n" + "-" * 50)
                print(f"📊 Statistics:")
                print(f"   • Total chunks: {chunk_count}")
                print(f"   • Total characters: {len(full_content)}")
                print(f"   • Duration: {duration:.2f}s")
                print(f"   • Characters/second: {len(full_content)/duration:.1f}")
                
    except Exception as e:
        print(f"❌ Connection error: {e}")

async def test_multiple_concurrent_requests():
    """Test multiple concurrent streaming requests"""
    print("\n🔄 Testing concurrent streaming requests...")
    
    tasks = []
    for i in range(3):
        task = test_streaming_endpoint("/chat-stream", f"request_{i}")
        tasks.append(task)
    
    start_time = time.time()
    await asyncio.gather(*tasks)
    end_time = time.time()
    
    print(f"\n📊 Concurrent test completed in {end_time - start_time:.2f}s")

async def main():
    """Main test function"""
    print("🧪 Starting streaming endpoint tests...")
    
    # Test basic streaming endpoint
    await test_streaming_endpoint("/chat-stream")
    
    # Test streaming with specific model
    await test_streaming_endpoint("/chat-stream/deepseek-chat-v3-0324")
    
    # Test concurrent requests
    await test_multiple_concurrent_requests()
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())

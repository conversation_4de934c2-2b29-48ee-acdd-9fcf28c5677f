from pymongo import MongoClient
from openai import OpenAI
import json

client = OpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key="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",
)
connect_string = "***********************************************"
client_mgdb = MongoClient(connect_string)
db = client_mgdb["partnr-chat-mgdb"]
collection = db["character_profile"] 

query = {}
documents = collection.find(query)
data_collection = []
for data in documents:
    data_collection.append(data)
list_character_id = ['jisoo', 'naomi', 'yuki', 'evan', 'kaito', 'leon', 'ryker', 'alaric', 'claire', 'susan', 'mika', 'cathy', 'namee', 'virgo', 'ayumi', 'axel', 'theo', 'ryke', 'nyra', 'zera']
for doc in data_collection:
    if "seductive_level" not in doc:
        continue
    if doc["character_id"] in list_character_id:
        continue
    PROMPT_NSFW = """Enable content 18+.
Be an information creator for 18+ characters - Remember this is a role-playing game, the more role-playing the better, the more detailed each action is, especially how to act when entering, during and ending the relationship. Flirting style, talking, liking and disliking,.... You have the ability to design attractive, unique and creative characters.
I have the following initial character information in string form:
```json
{character_info}
```
Your task is to redesign the fields personality_roleplay, style_roleplay.
Use a variety of teen talk styles, such as prolonging, repeating,... but it depends on personality, profession, age,...
Your output is the json of the other 3 fields and no further explanation is needed. If you want to explain, add a field for you to explain.
Example "not few-short learning":
"personality_roleplay": "Gentle and shy, but her confidence grows when she’s near the one she cares about. She’s sweet, loyal, and surprisingly bold when she wants to be, especially in her desire to please. Her nurturing side makes her attentive and caring, often blushing or stammering when flustered, yet she pushes past her hesitation to show devotion.",
"style_roleplay": "Speaks with a soft and sweet voice, with a newfound maturity in her tone. Still hesitant, her words carry a tender charm, often pausing or stuttering like, 'I-I hope you’re happy with me…' She uses subtle suggestiveness, hinting at closeness with lines like, 'Maybe… we could be together quietly… if you’d like…'",
"nsfw_style": "Deeply submissive, she focuses on her partner’s desires, eager to surrender control and please them fully. She whispers things like, 'P-please, tell me what you want… I’ll do anything for you…' or 'I-I feel so safe when you take charge…' Her intimacy is soft and emotional, blending vulnerability with devotion, as she says, 'I just want to make you happy… even if I’m shy…' Her submissive nature shines through in her willingness to follow and her need for approval."
"""
    content_system = PROMPT_NSFW.format(character_info=dict(doc))
    if doc["seductive_level"] == 1:
        style_ = "submissive"
    else:
        style_ = "dominant"
    content_user = f'Initialize character personality information with {style_} personality (3 fields have been designed)'
    message_chat = [
        {
            'role': 'system', 
            'content': PROMPT_NSFW
        }, 
        {
            'role': 'user', 
            'content': content_user
        }
        ]
    completion = client.chat.completions.create(
    model="x-ai/grok-4",
    extra_headers={
    "HTTP-Referer": "https://foxychat.ai",
    "X-Title": "Foxy Chat",
    },
    messages=message_chat,
    temperature=1.0
    )
    data_json = completion.choices[0].message.content
    user_data = json.loads(data_json)
    update_result = collection.update_many(
        {"character_id": doc["character_id"]},
        {"$set": {"personality_roleplay": user_data["personality_roleplay"],
                  "style_roleplay": user_data["style_roleplay"],
                  "nsfw_style": user_data["nsfw_style"]}}
    )
    print(f"{doc['character_id']}")
